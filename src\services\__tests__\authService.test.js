import { describe, it, expect, beforeEach, vi } from 'vitest';
import AuthService from '../authService.js';

// Mock the API client
vi.mock('../../api/index.js', () => ({
  apiClient: {
    post: vi.fn(),
    setAuthToken: vi.fn(),
  },
  API_ENDPOINTS: {
    auth: {
      emailVerification: '/auth/email-verification',
      verifyOTP: '/auth/verify-otp',
      validateOTP: '/auth/validate-otp',
      setPassword: '/auth/set-password',
      createAccount: '/auth/create-account',
      signin: '/auth/signin',
      login: '/auth/login',
      logout: '/auth/logout',
      resendVerification: '/auth/resend-verification',
    }
  },
  withErrorHandling: vi.fn(),
}));

// Mock the crypto utilities
vi.mock('../../utils/crypto.js', () => ({
  encryptPassword: vi.fn(),
}));

describe('AuthService', () => {
  let authService;
  let mockApiClient;
  let mockWithErrorHandling;
  let mockEncryptPassword;

  beforeEach(async () => {
    vi.clearAllMocks();

    // Import the mocked modules
    const { apiClient, withErrorHandling } = await import('../../api/index.js');
    const { encryptPassword } = await import('../../utils/crypto.js');
    mockApiClient = apiClient;
    mockWithErrorHandling = withErrorHandling;
    mockEncryptPassword = encryptPassword;

    // Setup default mock for password encryption
    mockEncryptPassword.mockResolvedValue('encrypted_password_hash');

    authService = new AuthService();
  });

  describe('sendEmailVerification', () => {
    it('should send email verification successfully', async () => {
      const mockResponse = { success: true, data: { message: 'Verification email sent' } };
      mockWithErrorHandling.mockResolvedValue(mockResponse);

      const result = await authService.sendEmailVerification('<EMAIL>', 'captcha-token');

      expect(mockWithErrorHandling).toHaveBeenCalledWith(
        expect.any(Function),
        { operation: 'sendEmailVerification', email: '<EMAIL>' }
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle email verification failure', async () => {
      const mockError = { success: false, error: 'API Error' };
      mockWithErrorHandling.mockResolvedValue(mockError);

      const result = await authService.sendEmailVerification('<EMAIL>', 'captcha-token');

      expect(result).toEqual(mockError);
    });
  });

  describe('validateOTP', () => {
    it('should validate OTP successfully with all parameters', async () => {
      const mockResponse = { success: true, data: { message: 'OTP validated successfully' } };
      mockWithErrorHandling.mockResolvedValue(mockResponse);

      const result = await authService.validateOTP('<EMAIL>', '123456', 'token123', 'sign_up');

      expect(mockWithErrorHandling).toHaveBeenCalledWith(
        expect.any(Function),
        { operation: 'validateOTP', email: '<EMAIL>', purpose: 'sign_up' }
      );

      expect(result).toEqual(mockResponse);
    });

    it('should validate OTP with default purpose', async () => {
      const mockResponse = { success: true, data: { message: 'OTP validated successfully' } };
      mockWithErrorHandling.mockResolvedValue(mockResponse);

      const result = await authService.validateOTP('<EMAIL>', '123456', 'token123');

      expect(mockWithErrorHandling).toHaveBeenCalledWith(
        expect.any(Function),
        { operation: 'validateOTP', email: '<EMAIL>', purpose: 'sign_up' }
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle OTP validation failure', async () => {
      const mockError = { success: false, error: 'Invalid OTP' };
      mockWithErrorHandling.mockResolvedValue(mockError);

      const result = await authService.validateOTP('<EMAIL>', '123456', 'token123');

      expect(result).toEqual(mockError);
    });
  });

  describe('setPassword', () => {
    it('should set password successfully without token', async () => {
      const mockResponse = { success: true, data: { message: 'Password set successfully' } };
      mockWithErrorHandling.mockResolvedValue(mockResponse);

      const result = await authService.setPassword('<EMAIL>', 'password123', 'password123');

      expect(mockWithErrorHandling).toHaveBeenCalledWith(
        expect.any(Function),
        { operation: 'setPassword', email: '<EMAIL>', hasToken: false }
      );

      expect(result).toEqual(mockResponse);
    });

    it('should set password successfully with token', async () => {
      const mockResponse = { success: true, data: { message: 'Password set successfully' } };
      mockWithErrorHandling.mockResolvedValue(mockResponse);

      const result = await authService.setPassword('<EMAIL>', 'password123', 'password123', 'token123');

      expect(mockWithErrorHandling).toHaveBeenCalledWith(
        expect.any(Function),
        { operation: 'setPassword', email: '<EMAIL>', hasToken: true }
      );

      expect(result).toEqual(mockResponse);
    });

    it('should handle password mismatch', async () => {
      const result = await authService.setPassword('<EMAIL>', 'password123', 'password456');

      expect(result).toEqual({
        success: false,
        error: 'Passwords do not match'
      });

      // Should not call API if passwords don't match
      expect(mockWithErrorHandling).not.toHaveBeenCalled();
    });

    it('should handle password mismatch with token', async () => {
      const result = await authService.setPassword('<EMAIL>', 'password123', 'password456', 'token123');

      expect(result).toEqual({
        success: false,
        error: 'Passwords do not match'
      });

      // Should not call API if passwords don't match
      expect(mockWithErrorHandling).not.toHaveBeenCalled();
    });

    it('should handle set password failure', async () => {
      const mockError = { success: false, error: 'Password requirements not met' };
      mockWithErrorHandling.mockResolvedValue(mockError);

      const result = await authService.setPassword('<EMAIL>', 'weak', 'weak');

      expect(result).toEqual(mockError);
    });
  });

});
