import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import AuthService from '../services/authService.js';

// Initialize auth service
const authService = new AuthService();

// Hardcoded user credentials for demo
const USERS = [
  { id: 'user1', user: 'user1', email: '<EMAIL>', password: 'pass1' },
  { id: 'user2', user: 'user2', email: '<EMAIL>', password: 'pass2' }
];

export const useAuthStore = create(
  persist(
    (set) => ({
      currentUser: null,
      isAuthenticated: false,

      login: async (email, password, recaptchaToken) => {
        try {
          // Use the API service for signin
          const response = await authService.signin(email, password, recaptchaToken);

          if (response && response.statusCode === 200) {
            // Extract user data from response
            const userData = {
              id: response.data?.id || response.data?.userId,
              email: email,
              ...response.data
            };

            set({
              currentUser: userData,
              isAuthenticated: true
            });
            return true;
          }

          return false;
        } catch (error) {
          console.error('<PERSON><PERSON> failed:', error);

          // Fallback to hardcoded users for demo purposes
          const user = USERS.find(
            (u) => u.email.toLowerCase() === email.toLowerCase() && u.password === password
          );
          if (user) {
            set({ currentUser: user, isAuthenticated: true });
            return true;
          }

          return false;
        }
      },

      thirdPartyLogin: (firebaseUser) => {
        console.log('Firebase user data:', firebaseUser);
        set({
          currentUser: {
            id: firebaseUser.uid,
            email: firebaseUser.email || firebaseUser.providerData?.[0]?.email,
            displayName: firebaseUser.displayName || firebaseUser.providerData?.[0]?.displayName,
            photoURL: firebaseUser.photoURL || firebaseUser.providerData?.[0]?.photoURL,
            providerData: firebaseUser.providerData
          },
          isAuthenticated: true
        });
      },

      logout: () => {
        set({ currentUser: null, isAuthenticated: false });
      }
    }),
    { name: 'auth-storage' }
  )
);
